import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AuthService from '../services/AuthService';

const Header = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Get user data from local storage
    const userData = AuthService.getCurrentUserData();
    setUser(userData);
  }, []);

  const handleLogout = async () => {
    setLoading(true);
    try {
      await AuthService.logout();
      setUser(null);
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      AuthService.clearTokens();
      setUser(null);
      navigate('/login');
    } finally {
      setLoading(false);
    }
  };

  const getRoleBadgeClass = (role) => {
    switch (role) {
      case 'admin':
        return 'bg-danger';
      case 'user':
        return 'bg-primary';
      default:
        return 'bg-secondary';
    }
  };

  if (!user) {
    return null; // Don't show header if not authenticated
  }

  return (
    <nav className="navbar navbar-expand-lg navbar-dark bg-dark shadow-sm">
      <div className="container">
        {/* Brand */}
        <a className="navbar-brand" href="/">
          <i className="bi bi-building me-2"></i>
          Employee Management System
        </a>

        {/* Toggle button for mobile */}
        <button
          className="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </button>

        {/* Navigation items */}
        <div className="collapse navbar-collapse" id="navbarNav">
          <ul className="navbar-nav me-auto">
            <li className="nav-item">
              <a className="nav-link" href="/">
                <i className="bi bi-house me-1"></i>
                Dashboard
              </a>
            </li>
            <li className="nav-item">
              <a className="nav-link" href="/employees">
                <i className="bi bi-people me-1"></i>
                Employees
              </a>
            </li>
            {user.role === 'admin' && (
              <li className="nav-item">
                <a className="nav-link" href="/admin">
                  <i className="bi bi-gear me-1"></i>
                  Admin Panel
                </a>
              </li>
            )}
          </ul>

          {/* User info and logout */}
          <ul className="navbar-nav">
            <li className="nav-item dropdown">
              <a
                className="nav-link dropdown-toggle d-flex align-items-center"
                href="#"
                id="navbarDropdown"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i className="bi bi-person-circle me-2"></i>
                <span className="me-2">{user.username}</span>
                <span className={`badge ${getRoleBadgeClass(user.role)} me-2`}>
                  {user.role}
                </span>
              </a>
              <ul className="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                <li>
                  <div className="dropdown-item-text">
                    <div className="d-flex flex-column">
                      <span className="fw-bold">{user.username}</span>
                      <small className="text-muted">{user.email}</small>
                      <small className="text-muted">
                        Role: <span className={`badge ${getRoleBadgeClass(user.role)}`}>
                          {user.role}
                        </span>
                      </small>
                    </div>
                  </div>
                </li>
                <li><hr className="dropdown-divider" /></li>
                <li>
                  <a className="dropdown-item" href="/profile">
                    <i className="bi bi-person me-2"></i>
                    Profile
                  </a>
                </li>
                <li>
                  <a className="dropdown-item" href="/settings">
                    <i className="bi bi-gear me-2"></i>
                    Settings
                  </a>
                </li>
                <li><hr className="dropdown-divider" /></li>
                <li>
                  <button
                    className="dropdown-item text-danger"
                    onClick={handleLogout}
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Signing out...
                      </>
                    ) : (
                      <>
                        <i className="bi bi-box-arrow-right me-2"></i>
                        Sign Out
                      </>
                    )}
                  </button>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  );
};

export default Header;
