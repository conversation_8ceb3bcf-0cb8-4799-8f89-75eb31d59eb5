import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import AuthService from '../services/AuthService';

const ProtectedRoute = ({ children, requiredRole = null, adminOnly = false }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(null); // null = checking, true/false = result
  const [userRole, setUserRole] = useState(null);
  const [loading, setLoading] = useState(true);
  const location = useLocation();

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      // Check if user has valid tokens
      if (!AuthService.isAuthenticated()) {
        setIsAuthenticated(false);
        setLoading(false);
        return;
      }

      // Verify token with server
      const response = await AuthService.verifyToken();
      
      if (response.data.valid) {
        setIsAuthenticated(true);
        setUserRole(response.data.user.role);
      } else {
        setIsAuthenticated(false);
        AuthService.clearTokens(); // Clear invalid tokens
      }
    } catch (error) {
      console.error('Authentication check failed:', error);
      setIsAuthenticated(false);
      AuthService.clearTokens(); // Clear invalid tokens
    } finally {
      setLoading(false);
    }
  };

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <div className="mt-2">
            <small className="text-muted">Verifying authentication...</small>
          </div>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role-based access
  if (adminOnly && userRole !== 'admin') {
    return (
      <div className="container mt-5">
        <div className="row justify-content-center">
          <div className="col-md-6">
            <div className="card border-danger">
              <div className="card-body text-center">
                <i className="bi bi-shield-exclamation text-danger" style={{ fontSize: '3rem' }}></i>
                <h4 className="card-title text-danger mt-3">Access Denied</h4>
                <p className="card-text">
                  You don't have permission to access this page. 
                  Administrator privileges are required.
                </p>
                <div className="mt-3">
                  <small className="text-muted">
                    Current role: <span className="badge bg-secondary">{userRole}</span>
                  </small>
                </div>
                <div className="mt-3">
                  <button 
                    className="btn btn-outline-primary"
                    onClick={() => window.history.back()}
                  >
                    <i className="bi bi-arrow-left me-1"></i>
                    Go Back
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Check specific required role
  if (requiredRole && userRole !== requiredRole && userRole !== 'admin') {
    return (
      <div className="container mt-5">
        <div className="row justify-content-center">
          <div className="col-md-6">
            <div className="card border-warning">
              <div className="card-body text-center">
                <i className="bi bi-person-exclamation text-warning" style={{ fontSize: '3rem' }}></i>
                <h4 className="card-title text-warning mt-3">Insufficient Permissions</h4>
                <p className="card-text">
                  You don't have the required role to access this page.
                </p>
                <div className="mt-3">
                  <small className="text-muted">
                    Required role: <span className="badge bg-primary">{requiredRole}</span><br />
                    Your role: <span className="badge bg-secondary">{userRole}</span>
                  </small>
                </div>
                <div className="mt-3">
                  <button 
                    className="btn btn-outline-primary"
                    onClick={() => window.history.back()}
                  >
                    <i className="bi bi-arrow-left me-1"></i>
                    Go Back
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // User is authenticated and has required permissions
  return children;
};

export default ProtectedRoute;
