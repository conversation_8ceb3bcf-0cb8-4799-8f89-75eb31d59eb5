import axios from 'axios';

const BFF_AUTH_URL = 'http://localhost:4000/api/auth';

// Create axios instance for auth requests
const authClient = axios.create({
  baseURL: BFF_AUTH_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Token storage keys
const ACCESS_TOKEN_KEY = 'employee_access_token';
const REFRESH_TOKEN_KEY = 'employee_refresh_token';
const USER_DATA_KEY = 'employee_user_data';

class AuthService {
  constructor() {
    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for automatic token handling
   */
  setupInterceptors() {
    // Request interceptor to add token to headers
    authClient.interceptors.request.use(
      (config) => {
        const token = this.getAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle token refresh
    authClient.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = this.getRefreshToken();
            if (refreshToken) {
              const response = await this.refreshAccessToken(refreshToken);
              const { accessToken } = response.data.tokens;
              
              this.setAccessToken(accessToken);
              originalRequest.headers.Authorization = `Bearer ${accessToken}`;
              
              return authClient(originalRequest);
            }
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError);
            this.logout();
            window.location.href = '/login';
          }
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Login user with username/email and password
   * @param {string} identifier - Username or email
   * @param {string} password - Password
   * @returns {Promise<Object>} Login response
   */
  async login(identifier, password) {
    try {
      console.log('🔑 Attempting login for:', identifier);
      
      const response = await authClient.post('/login', {
        identifier,
        password
      });

      const { user, tokens } = response.data.data;

      // Store tokens and user data
      this.setAccessToken(tokens.accessToken);
      this.setRefreshToken(tokens.refreshToken);
      this.setUserData(user);

      console.log('✅ Login successful for user:', user.username);
      return response.data;

    } catch (error) {
      console.error('❌ Login failed:', error.response?.data || error.message);
      throw this.handleAuthError(error);
    }
  }

  /**
   * Register new user (admin only)
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Registration response
   */
  async register(userData) {
    try {
      console.log('📝 Attempting registration for:', userData.username);
      
      const response = await authClient.post('/register', userData);

      console.log('✅ Registration successful for user:', userData.username);
      return response.data;

    } catch (error) {
      console.error('❌ Registration failed:', error.response?.data || error.message);
      throw this.handleAuthError(error);
    }
  }

  /**
   * Logout user
   * @returns {Promise<void>}
   */
  async logout() {
    try {
      const refreshToken = this.getRefreshToken();
      
      if (refreshToken) {
        await authClient.post('/logout', { refreshToken });
      }

      console.log('✅ Logout successful');
    } catch (error) {
      console.error('❌ Logout error:', error);
    } finally {
      // Clear local storage regardless of API call result
      this.clearTokens();
    }
  }

  /**
   * Refresh access token
   * @param {string} refreshToken - Refresh token
   * @returns {Promise<Object>} Refresh response
   */
  async refreshAccessToken(refreshToken) {
    try {
      const response = await axios.post(`${BFF_AUTH_URL}/refresh`, {
        refreshToken
      });

      console.log('🔄 Token refreshed successfully');
      return response.data;

    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      throw error;
    }
  }

  /**
   * Get current user profile
   * @returns {Promise<Object>} User profile
   */
  async getCurrentUser() {
    try {
      const response = await authClient.get('/me');
      
      // Update stored user data
      this.setUserData(response.data.data.user);
      
      return response.data;
    } catch (error) {
      console.error('❌ Failed to get current user:', error);
      throw this.handleAuthError(error);
    }
  }

  /**
   * Verify token validity
   * @returns {Promise<Object>} Verification response
   */
  async verifyToken() {
    try {
      const response = await authClient.get('/verify');
      return response.data;
    } catch (error) {
      console.error('❌ Token verification failed:', error);
      throw this.handleAuthError(error);
    }
  }

  /**
   * Get all users (admin only)
   * @returns {Promise<Object>} Users list
   */
  async getAllUsers() {
    try {
      const response = await authClient.get('/users');
      return response.data;
    } catch (error) {
      console.error('❌ Failed to get users:', error);
      throw this.handleAuthError(error);
    }
  }

  // Token management methods
  getAccessToken() {
    return localStorage.getItem(ACCESS_TOKEN_KEY);
  }

  setAccessToken(token) {
    localStorage.setItem(ACCESS_TOKEN_KEY, token);
  }

  getRefreshToken() {
    return localStorage.getItem(REFRESH_TOKEN_KEY);
  }

  setRefreshToken(token) {
    localStorage.setItem(REFRESH_TOKEN_KEY, token);
  }

  getUserData() {
    const userData = localStorage.getItem(USER_DATA_KEY);
    return userData ? JSON.parse(userData) : null;
  }

  setUserData(userData) {
    localStorage.setItem(USER_DATA_KEY, JSON.stringify(userData));
  }

  clearTokens() {
    localStorage.removeItem(ACCESS_TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(USER_DATA_KEY);
  }

  /**
   * Check if user is authenticated
   * @returns {boolean} True if authenticated
   */
  isAuthenticated() {
    const token = this.getAccessToken();
    const userData = this.getUserData();
    return !!(token && userData);
  }

  /**
   * Check if user has specific role
   * @param {string} role - Role to check
   * @returns {boolean} True if user has role
   */
  hasRole(role) {
    const userData = this.getUserData();
    return userData?.role === role || userData?.role === 'admin';
  }

  /**
   * Check if user is admin
   * @returns {boolean} True if user is admin
   */
  isAdmin() {
    return this.hasRole('admin');
  }

  /**
   * Get current user info from local storage
   * @returns {Object|null} User data or null
   */
  getCurrentUserData() {
    return this.getUserData();
  }

  /**
   * Handle authentication errors
   * @param {Error} error - Error object
   * @returns {Error} Formatted error
   */
  handleAuthError(error) {
    if (error.response?.data?.error) {
      const { message, code, status } = error.response.data.error;
      const authError = new Error(message);
      authError.code = code;
      authError.status = status;
      return authError;
    }
    
    return new Error(error.message || 'Authentication failed');
  }

  /**
   * Create authenticated axios instance for other services
   * @returns {Object} Configured axios instance
   */
  createAuthenticatedClient(baseURL) {
    const client = axios.create({
      baseURL,
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Add token to requests
    client.interceptors.request.use(
      (config) => {
        const token = this.getAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Handle token refresh on 401
    client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = this.getRefreshToken();
            if (refreshToken) {
              const response = await this.refreshAccessToken(refreshToken);
              const { accessToken } = response.data.tokens;
              
              this.setAccessToken(accessToken);
              originalRequest.headers.Authorization = `Bearer ${accessToken}`;
              
              return client(originalRequest);
            }
          } catch (refreshError) {
            this.logout();
            window.location.href = '/login';
          }
        }

        return Promise.reject(error);
      }
    );

    return client;
  }
}

export default new AuthService();
