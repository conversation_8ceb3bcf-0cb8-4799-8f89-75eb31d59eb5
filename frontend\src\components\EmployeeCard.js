import React from 'react';

const EmployeeCard = ({ employee, onDelete, isDeleting }) => {
  if (!employee) return null;

  if (employee?.employeeId === -1) {
    return (
      <div className="card mb-4 border-danger">
        <div className="card-header bg-danger text-white">
          <h5 className="card-title mb-0">Employee</h5>
        </div>
        <div className="card-body">
          <p className="text-danger">Error loading employee data</p>
        </div>
      </div>
    );
  }

  const handleDelete = () => {
    if (window.confirm(`Are you sure you want to delete employee "${employee.name}" (ID: ${employee.employeeId})?`)) {
      onDelete(employee.employeeId);
    }
  };

  return (
    <div className="card mb-4">
      <div className="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 className="card-title mb-0">Employee</h5>
        {onDelete && (
          <button
            className="btn btn-sm btn-outline-light"
            onClick={handleDelete}
            disabled={isDeleting}
            title="Delete Employee"
          >
            {isDeleting ? (
              <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            ) : (
              <i className="bi bi-trash"></i>
            )}
          </button>
        )}
      </div>
      <div className="card-body">
        <p><strong>Name:</strong> {employee.name}</p>
        <p><strong>ID:</strong> {employee.employeeId}</p>
      </div>
    </div>
  );
};

export default EmployeeCard;
