import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import AuthService from './services/AuthService';
import Login from './components/Login';
import ProtectedRoute from './components/ProtectedRoute';
import Header from './components/Header';
import EmployeeDashboard from './components/EmployeeDashboard';

function App() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Check if user is already authenticated
    const userData = AuthService.getCurrentUserData();
    setUser(userData);
  }, []);

  const handleLogin = (userData) => {
    setUser(userData);
  };

  return (
    <Router>
      <div className="App">
        {/* Header - only show if authenticated */}
        {AuthService.isAuthenticated() && <Header />}

        <Routes>
          {/* Login Route */}
          <Route
            path="/login"
            element={<Login onLogin={handleLogin} />}
          />

          {/* Protected Dashboard Route */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <EmployeeDashboard />
              </ProtectedRoute>
            }
          />

          {/* Redirect any unknown routes to dashboard */}
          <Route
            path="*"
            element={<Navigate to="/" replace />}
          />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
