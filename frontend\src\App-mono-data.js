import React, { useState, useEffect } from 'react';
import EmployeeService from './services/EmployeeService';
import EmployeeCard from './components/EmployeeCard';
import DepartmentCard from './components/DepartmentCard';
import ManagerCard from './components/ManagerCard';
import ErrorAlert from './components/ErrorAlert';
import LoadingSpinner from './components/LoadingSpinner';

function App() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await EmployeeService.getEmployeeData();
      setData(result);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.message || 'Failed to fetch employee data');
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mt-5">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Employee Dashboard</h1>
        <button 
          className="btn btn-primary" 
          onClick={fetchData} 
          disabled={loading}
        >
          {loading ? 'Refreshing...' : 'Refresh Data'}
        </button>
      </div>

      {loading ? (
        <LoadingSpinner />
      ) : error ? (
        <ErrorAlert message={error} retryFn={fetchData} />
      ) : (
        <div className="row">
          {data?.employee && (
            <div className="col-md-4">
              <EmployeeCard employee={data.employee} />
            </div>
          )}
          
          
          {data?.department && (
            <div className="col-md-4">
              <DepartmentCard department={data.department} />
            </div>
          )}
          
          {data?.manager && (
            <div className="col-md-4">
              <ManagerCard manager={data.manager} />
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default App;